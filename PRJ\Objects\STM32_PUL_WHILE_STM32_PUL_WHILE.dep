Dependencies for Project 'STM32_PUL_WHILE', Target 'STM32_PUL_WHILE': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (..\APP\main.c)(0x64EB5D7E)(--c99 --gnu -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\APP -I ..\BSP -I ..\CMSIS -I ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc -I ..\DRIVERS

-I"D:\keil c51\ARM\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include"

-D__UVISION_VERSION="541" -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\BSP\board.h)(0x64EB5D9D)
I (..\CMSIS\stm32f10x.h)(0x4D783CB4)
I (..\CMSIS\core_cm3.h)(0x4D523B58)
I (D:\keil c51\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\CMSIS\system_stm32f10x.h)(0x4D783CAA)
I (..\APP\stm32f10x_conf.h)(0x603A7410)
I (D:\keil c51\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_gpio.h)(0x4D783BB4)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_rcc.h)(0x4D783BB4)
I (..\DRIVERS\delay.h)(0x64EB5DA7)
F (..\APP\stm32f10x_conf.h)(0x603A7410)()
F (..\APP\stm32f10x_it.c)(0x4D99A59E)(--c99 --gnu -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\APP -I ..\BSP -I ..\CMSIS -I ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc -I ..\DRIVERS

-I"D:\keil c51\ARM\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include"

-D__UVISION_VERSION="541" -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o .\objects\stm32f10x_it.o --omf_browse .\objects\stm32f10x_it.crf --depend .\objects\stm32f10x_it.d)
I (..\APP\stm32f10x_it.h)(0x4D99A59E)
I (..\CMSIS\stm32f10x.h)(0x4D783CB4)
I (..\CMSIS\core_cm3.h)(0x4D523B58)
I (D:\keil c51\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\CMSIS\system_stm32f10x.h)(0x4D783CAA)
I (..\APP\stm32f10x_conf.h)(0x603A7410)
I (D:\keil c51\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_gpio.h)(0x4D783BB4)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_rcc.h)(0x4D783BB4)
F (..\BSP\board.c)(0x64EB5D9D)(--c99 --gnu -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\APP -I ..\BSP -I ..\CMSIS -I ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc -I ..\DRIVERS

-I"D:\keil c51\ARM\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include"

-D__UVISION_VERSION="541" -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o .\objects\board.o --omf_browse .\objects\board.crf --depend .\objects\board.d)
I (..\BSP\board.h)(0x64EB5D9D)
I (..\CMSIS\stm32f10x.h)(0x4D783CB4)
I (..\CMSIS\core_cm3.h)(0x4D523B58)
I (D:\keil c51\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\CMSIS\system_stm32f10x.h)(0x4D783CAA)
I (..\APP\stm32f10x_conf.h)(0x603A7410)
I (D:\keil c51\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_gpio.h)(0x4D783BB4)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_rcc.h)(0x4D783BB4)
F (..\BSP\board.h)(0x64EB5D9D)()
F (..\CMSIS\core_cm3.c)(0x4C0C587E)(--c99 --gnu -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\APP -I ..\BSP -I ..\CMSIS -I ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc -I ..\DRIVERS

-I"D:\keil c51\ARM\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include"

-D__UVISION_VERSION="541" -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o .\objects\core_cm3.o --omf_browse .\objects\core_cm3.crf --depend .\objects\core_cm3.d)
I (D:\keil c51\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
F (..\CMSIS\startup_stm32f10x_hd.s)(0x4D783CDE)(--cpu Cortex-M3 -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I"D:\keil c51\ARM\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include"

--pd "__UVISION_VERSION SETA 541" --pd "STM32F10X_MD SETA 1"

--list .\listings\startup_stm32f10x_hd.lst --xref -o .\objects\startup_stm32f10x_hd.o --depend .\objects\startup_stm32f10x_hd.d)
F (..\CMSIS\system_stm32f10x.c)(0x4D783CB0)(--c99 --gnu -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\APP -I ..\BSP -I ..\CMSIS -I ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc -I ..\DRIVERS

-I"D:\keil c51\ARM\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include"

-D__UVISION_VERSION="541" -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o .\objects\system_stm32f10x.o --omf_browse .\objects\system_stm32f10x.crf --depend .\objects\system_stm32f10x.d)
I (..\CMSIS\stm32f10x.h)(0x4D783CB4)
I (..\CMSIS\core_cm3.h)(0x4D523B58)
I (D:\keil c51\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\CMSIS\system_stm32f10x.h)(0x4D783CAA)
I (..\APP\stm32f10x_conf.h)(0x603A7410)
I (D:\keil c51\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_gpio.h)(0x4D783BB4)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_rcc.h)(0x4D783BB4)
F (..\DRIVERS\delay.c)(0x64EB5DA7)(--c99 --gnu -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\APP -I ..\BSP -I ..\CMSIS -I ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc -I ..\DRIVERS

-I"D:\keil c51\ARM\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include"

-D__UVISION_VERSION="541" -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o .\objects\delay.o --omf_browse .\objects\delay.crf --depend .\objects\delay.d)
I (..\DRIVERS\delay.h)(0x64EB5DA7)
I (..\CMSIS\stm32f10x.h)(0x4D783CB4)
I (..\CMSIS\core_cm3.h)(0x4D523B58)
I (D:\keil c51\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\CMSIS\system_stm32f10x.h)(0x4D783CAA)
I (..\APP\stm32f10x_conf.h)(0x603A7410)
I (D:\keil c51\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_gpio.h)(0x4D783BB4)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_rcc.h)(0x4D783BB4)
F (..\DRIVERS\delay.h)(0x64EB5DA7)()
F (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_gpio.c)(0x4D79EEC6)(--c99 --gnu -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\APP -I ..\BSP -I ..\CMSIS -I ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc -I ..\DRIVERS

-I"D:\keil c51\ARM\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include"

-D__UVISION_VERSION="541" -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o .\objects\stm32f10x_gpio.o --omf_browse .\objects\stm32f10x_gpio.crf --depend .\objects\stm32f10x_gpio.d)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_gpio.h)(0x4D783BB4)
I (..\CMSIS\stm32f10x.h)(0x4D783CB4)
I (..\CMSIS\core_cm3.h)(0x4D523B58)
I (D:\keil c51\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\CMSIS\system_stm32f10x.h)(0x4D783CAA)
I (..\APP\stm32f10x_conf.h)(0x603A7410)
I (D:\keil c51\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_rcc.h)(0x4D783BB4)
F (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_rcc.c)(0x4D783BB4)(--c99 --gnu -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\APP -I ..\BSP -I ..\CMSIS -I ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc -I ..\DRIVERS

-I"D:\keil c51\ARM\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include"

-D__UVISION_VERSION="541" -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o .\objects\stm32f10x_rcc.o --omf_browse .\objects\stm32f10x_rcc.crf --depend .\objects\stm32f10x_rcc.d)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_rcc.h)(0x4D783BB4)
I (..\CMSIS\stm32f10x.h)(0x4D783CB4)
I (..\CMSIS\core_cm3.h)(0x4D523B58)
I (D:\keil c51\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\CMSIS\system_stm32f10x.h)(0x4D783CAA)
I (..\APP\stm32f10x_conf.h)(0x603A7410)
I (D:\keil c51\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_gpio.h)(0x4D783BB4)
