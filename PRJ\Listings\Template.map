Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.main) refers to board.o(i.board_init) for board_init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to delay.o(i.delay_cnt) for delay_cnt
    board.o(i.board_init) refers to board.o(i.clock_init) for clock_init
    board.o(i.board_init) refers to board.o(i.gpio_init) for gpio_init
    board.o(i.clock_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    board.o(i.gpio_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    board.o(i.gpio_init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    delay.o(i.delay_ms) refers to system_stm32f10x.o(.data) for SystemCoreClock
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetClocksFreq), (212 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rcc.o(.data), (20 bytes).

51 unused section(s) (total 2364 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ..\APP\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\APP\stm32f10x_it.c                    0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\BSP\board.c                           0x00000000   Number         0  board.o ABSOLUTE
    ..\CMSIS\core_cm3.c                      0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\startup_stm32f10x_hd.s          0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\system_stm32f10x.c              0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\DRIVERS\delay.c                       0x00000000   Number         0  delay.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\\CMSIS\\core_cm3.c                    0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000140   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000144   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000144   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000144   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000144   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000148   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x0800016c   Section       36  init.o(.text)
    i.BusFault_Handler                       0x08000190   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000194   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x08000196   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ResetBits                         0x080002ac   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.HardFault_Handler                      0x080002b0   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x080002b4   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080002b8   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x080002ba   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB2PeriphClockCmd                 0x080002bc   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.SVC_Handler                            0x080002dc   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080002de   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x080002df   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x080002e8   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x080002e9   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x080003c8   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080003cc   Section        0  system_stm32f10x.o(i.SystemInit)
    i.UsageFault_Handler                     0x0800042c   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__scatterload_copy                     0x08000430   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800043e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08000440   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.board_init                             0x0800044e   Section        0  board.o(i.board_init)
    i.clock_init                             0x0800045a   Section        0  board.o(i.clock_init)
    i.delay_cnt                              0x08000466   Section        0  delay.o(i.delay_cnt)
    i.delay_ms                               0x08000470   Section        0  delay.o(i.delay_ms)
    i.gpio_init                              0x080004d0   Section        0  board.o(i.gpio_init)
    i.main                                   0x080004fc   Section        0  main.o(i.main)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    STACK                                    0x20000018   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000141   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000145   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000145   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x08000149   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __scatterload                            0x0800016d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0800016d   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x08000191   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08000195   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x08000197   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ResetBits                           0x080002ad   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    HardFault_Handler                        0x080002b1   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x080002b5   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080002b9   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080002bb   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB2PeriphClockCmd                   0x080002bd   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    SVC_Handler                              0x080002dd   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_Handler                          0x080003c9   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x080003cd   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    UsageFault_Handler                       0x0800042d   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __scatterload_copy                       0x08000431   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800043f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08000441   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    board_init                               0x0800044f   Thumb Code    12  board.o(i.board_init)
    clock_init                               0x0800045b   Thumb Code    12  board.o(i.clock_init)
    delay_cnt                                0x08000467   Thumb Code    10  delay.o(i.delay_cnt)
    delay_ms                                 0x08000471   Thumb Code    90  delay.o(i.delay_ms)
    gpio_init                                0x080004d1   Thumb Code    38  board.o(i.gpio_init)
    main                                     0x080004fd   Thumb Code   106  main.o(i.main)
    Region$$Table$$Base                      0x0800056c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800058c   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    __initial_sp                             0x20000418   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000005a0, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000058c, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          147    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO          518  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO          521    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO          524    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO          526    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO          528    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO          529    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000004   Code   RO          536    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000144   0x08000144   0x00000000   Code   RO          531    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000144   0x08000144   0x00000000   Code   RO          533    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000144   0x08000144   0x00000004   Code   RO          522    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000148   0x08000148   0x00000024   Code   RO          148    .text               startup_stm32f10x_hd.o
    0x0800016c   0x0800016c   0x00000024   Code   RO          537    .text               mc_w.l(init.o)
    0x08000190   0x08000190   0x00000004   Code   RO           47    i.BusFault_Handler  stm32f10x_it.o
    0x08000194   0x08000194   0x00000002   Code   RO           48    i.DebugMon_Handler  stm32f10x_it.o
    0x08000196   0x08000196   0x00000116   Code   RO          210    i.GPIO_Init         stm32f10x_gpio.o
    0x080002ac   0x080002ac   0x00000004   Code   RO          217    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x080002b0   0x080002b0   0x00000004   Code   RO           49    i.HardFault_Handler  stm32f10x_it.o
    0x080002b4   0x080002b4   0x00000004   Code   RO           50    i.MemManage_Handler  stm32f10x_it.o
    0x080002b8   0x080002b8   0x00000002   Code   RO           51    i.NMI_Handler       stm32f10x_it.o
    0x080002ba   0x080002ba   0x00000002   Code   RO           52    i.PendSV_Handler    stm32f10x_it.o
    0x080002bc   0x080002bc   0x00000020   Code   RO          322    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x080002dc   0x080002dc   0x00000002   Code   RO           53    i.SVC_Handler       stm32f10x_it.o
    0x080002de   0x080002de   0x00000008   Code   RO          152    i.SetSysClock       system_stm32f10x.o
    0x080002e6   0x080002e6   0x00000002   PAD
    0x080002e8   0x080002e8   0x000000e0   Code   RO          153    i.SetSysClockTo72   system_stm32f10x.o
    0x080003c8   0x080003c8   0x00000002   Code   RO           54    i.SysTick_Handler   stm32f10x_it.o
    0x080003ca   0x080003ca   0x00000002   PAD
    0x080003cc   0x080003cc   0x00000060   Code   RO          155    i.SystemInit        system_stm32f10x.o
    0x0800042c   0x0800042c   0x00000004   Code   RO           55    i.UsageFault_Handler  stm32f10x_it.o
    0x08000430   0x08000430   0x0000000e   Code   RO          541    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800043e   0x0800043e   0x00000002   Code   RO          542    i.__scatterload_null  mc_w.l(handlers.o)
    0x08000440   0x08000440   0x0000000e   Code   RO          543    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800044e   0x0800044e   0x0000000c   Code   RO          110    i.board_init        board.o
    0x0800045a   0x0800045a   0x0000000c   Code   RO          111    i.clock_init        board.o
    0x08000466   0x08000466   0x0000000a   Code   RO          186    i.delay_cnt         delay.o
    0x08000470   0x08000470   0x00000060   Code   RO          187    i.delay_ms          delay.o
    0x080004d0   0x080004d0   0x0000002c   Code   RO          112    i.gpio_init         board.o
    0x080004fc   0x080004fc   0x00000070   Code   RO            1    i.main              main.o
    0x0800056c   0x0800056c   0x00000020   Data   RO          539    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800058c, Size: 0x00000418, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800058c   0x00000014   Data   RW          156    .data               system_stm32f10x.o
    0x20000014   0x080005a0   0x00000004   PAD
    0x20000018        -       0x00000400   Zero   RW          145    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        68          6          0          0          0       1598   board.o
         0          0          0          0          0         32   core_cm3.o
       106          6          0          0          0       1663   delay.o
       112          6          0          0          0     215943   main.o
        36          8        304          0       1024        864   startup_stm32f10x_hd.o
       282          0          0          0          0       3017   stm32f10x_gpio.o
        26          0          0          0          0       4642   stm32f10x_it.o
        32          6          0          0          0        697   stm32f10x_rcc.o
       328         28          0         20          0       2745   system_stm32f10x.o

    ----------------------------------------------------------------------
       994         <USER>        <GROUP>         20       1028     231201   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o

    ----------------------------------------------------------------------
        90         <USER>          <GROUP>          0          0         68   Library Totals
         0          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        90         16          0          0          0         68   mc_w.l

    ----------------------------------------------------------------------
        90         <USER>          <GROUP>          0          0         68   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      1084         76        336         20       1028     230485   Grand Totals
      1084         76        336         20       1028     230485   ELF Image Totals
      1084         76        336         20          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 1420 (   1.39kB)
    Total RW  Size (RW Data + ZI Data)              1048 (   1.02kB)
    Total ROM Size (Code + RO Data + RW Data)       1440 (   1.41kB)

==============================================================================

